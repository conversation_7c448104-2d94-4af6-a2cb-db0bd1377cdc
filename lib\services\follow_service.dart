import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

class FollowService {
  static final FollowService _instance = FollowService._internal();
  factory FollowService() => _instance;
  FollowService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String get _currentUserId => _auth.currentUser?.uid ?? '';

  /// Follow a user
  Future<bool> followUser(String targetUserId) async {
    if (_currentUserId.isEmpty || targetUserId == _currentUserId) {
      return false;
    }

    try {
      final batch = _firestore.batch();

      // Add to current user's following collection
      final followingRef = _firestore
          .collection('users')
          .doc(_currentUserId)
          .collection('following')
          .doc(targetUserId);

      batch.set(followingRef, {
        'followedAt': FieldValue.serverTimestamp(),
        'userId': targetUserId,
      });

      // Add to target user's followers collection
      final followerRef = _firestore
          .collection('users')
          .doc(targetUserId)
          .collection('followers')
          .doc(_currentUserId);

      batch.set(followerRef, {
        'followedAt': FieldValue.serverTimestamp(),
        'userId': _currentUserId,
      });

      // Update following count for current user
      final currentUserRef = _firestore.collection('users').doc(_currentUserId);
      batch.update(currentUserRef, {'followingCount': FieldValue.increment(1)});

      // Update followers count for target user
      final targetUserRef = _firestore.collection('users').doc(targetUserId);
      batch.update(targetUserRef, {'followersCount': FieldValue.increment(1)});

      await batch.commit();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Unfollow a user
  Future<bool> unfollowUser(String targetUserId) async {
    if (_currentUserId.isEmpty || targetUserId == _currentUserId) {
      return false;
    }

    try {
      final batch = _firestore.batch();

      // Remove from current user's following collection
      final followingRef = _firestore
          .collection('users')
          .doc(_currentUserId)
          .collection('following')
          .doc(targetUserId);

      batch.delete(followingRef);

      // Remove from target user's followers collection
      final followerRef = _firestore
          .collection('users')
          .doc(targetUserId)
          .collection('followers')
          .doc(_currentUserId);

      batch.delete(followerRef);

      // Update following count for current user
      final currentUserRef = _firestore.collection('users').doc(_currentUserId);
      batch.update(currentUserRef, {
        'followingCount': FieldValue.increment(-1),
      });

      // Update followers count for target user
      final targetUserRef = _firestore.collection('users').doc(targetUserId);
      batch.update(targetUserRef, {'followersCount': FieldValue.increment(-1)});

      await batch.commit();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if current user is following a specific user
  Stream<bool> isFollowingStream(String targetUserId) {
    if (_currentUserId.isEmpty || targetUserId == _currentUserId) {
      return Stream.value(false);
    }

    return _firestore
        .collection('users')
        .doc(_currentUserId)
        .collection('following')
        .doc(targetUserId)
        .snapshots()
        .map((snap) => snap.exists);
  }

  /// Get list of users that current user is following
  Stream<List<String>> getFollowingStream() {
    if (_currentUserId.isEmpty) {
      return Stream.value([]);
    }

    return _firestore
        .collection('users')
        .doc(_currentUserId)
        .collection('following')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.id).toList());
  }

  /// Get list of users following the current user
  Stream<List<String>> getFollowersStream() {
    if (_currentUserId.isEmpty) {
      return Stream.value([]);
    }

    return _firestore
        .collection('users')
        .doc(_currentUserId)
        .collection('followers')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.id).toList());
  }

  /// Get following count for a user
  Future<int> getFollowingCount(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      return userDoc.data()?['followingCount'] ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// Get followers count for a user
  Future<int> getFollowersCount(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      return userDoc.data()?['followersCount'] ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// Get suggested users to follow (users not currently followed)
  Stream<List<Map<String, dynamic>>> getSuggestedUsersStream() {
    if (_currentUserId.isEmpty) {
      return Stream.value([]);
    }

    return _firestore
        .collection('users')
        .where(FieldPath.documentId, isNotEqualTo: _currentUserId)
        .limit(20)
        .snapshots()
        .asyncMap((snapshot) async {
      final users = <Map<String, dynamic>>[];

      for (final doc in snapshot.docs) {
        final userData = doc.data();
        userData['id'] = doc.id;

        // Check if already following
        final isFollowing = await _firestore
            .collection('users')
            .doc(_currentUserId)
            .collection('following')
            .doc(doc.id)
            .get()
            .then((snap) => snap.exists);

        if (!isFollowing) {
          users.add(userData);
        }
      }

      return users;
    });
  }

  /// Make all users follow all other users
  Future<Map<String, dynamic>> makeAllUsersFollowEachOther() async {
    try {
      // Get all users
      final usersSnapshot = await _firestore.collection('users').get();
      final users = usersSnapshot.docs;

      if (users.length < 2) {
        return {
          'success': true,
          'message': 'Not enough users to create follow relationships',
          'processedUsers': 0,
          'totalRelationships': 0,
        };
      }

      int processedUsers = 0;
      int totalRelationships = 0;
      const int batchSize = 200; // Leave room for count updates
      List<WriteBatch> batches = [];
      WriteBatch currentBatch = _firestore.batch();
      int operationsInCurrentBatch = 0;

      for (int i = 0; i < users.length; i++) {
        final user1 = users[i];
        final user1Id = user1.id;

        for (int j = 0; j < users.length; j++) {
          if (i == j) continue; // Skip self

          final user2 = users[j];
          final user2Id = user2.id;

          // Check if user1 is already following user2
          final followingDoc = await _firestore
              .collection('users')
              .doc(user1Id)
              .collection('following')
              .doc(user2Id)
              .get();

          if (!followingDoc.exists) {
            // Create follow relationship
            final followingRef = _firestore
                .collection('users')
                .doc(user1Id)
                .collection('following')
                .doc(user2Id);

            currentBatch.set(followingRef, {
              'followedAt': FieldValue.serverTimestamp(),
              'userId': user2Id,
            });

            final followerRef = _firestore
                .collection('users')
                .doc(user2Id)
                .collection('followers')
                .doc(user1Id);

            currentBatch.set(followerRef, {
              'followedAt': FieldValue.serverTimestamp(),
              'userId': user1Id,
            });

            operationsInCurrentBatch += 2;
            totalRelationships++;
            debugPrint(
                'Creating relationship: $user1Id -> $user2Id (Total: $totalRelationships)');

            // Check if we need to start a new batch
            if (operationsInCurrentBatch >= batchSize) {
              batches.add(currentBatch);
              currentBatch = _firestore.batch();
              operationsInCurrentBatch = 0;
            }
          }
        }

        processedUsers++;
        debugPrint('Processed user: $user1Id (Total: $processedUsers)');
      }

      // Add the last batch if it has operations
      if (operationsInCurrentBatch > 0) {
        batches.add(currentBatch);
      }

      // Execute all batches
      for (final batch in batches) {
        debugPrint(
            'Executing batch: ${batches.indexOf(batch) + 1} of ${batches.length}');
        await batch.commit();
      }

      // Update user counts
      await _updateAllUserFollowCounts(users.map((doc) => doc.id).toList());

      return {
        'success': true,
        'message': 'Successfully created follow relationships for all users',
        'processedUsers': processedUsers,
        'totalRelationships': totalRelationships,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to create follow relationships: $e',
        'processedUsers': 0,
        'totalRelationships': 0,
      };
    }
  }

  /// Update follow counts for all users
  Future<void> _updateAllUserFollowCounts(List<String> userIds) async {
    const int batchSize = 500;
    List<WriteBatch> batches = [];
    WriteBatch currentBatch = _firestore.batch();
    int operationsInCurrentBatch = 0;

    for (final userId in userIds) {
      // Count following
      final followingSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('following')
          .get();

      // Count followers
      final followersSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('followers')
          .get();

      final userRef = _firestore.collection('users').doc(userId);
      currentBatch.update(userRef, {
        'followingCount': followingSnapshot.docs.length,
        'followersCount': followersSnapshot.docs.length,
      });

      operationsInCurrentBatch++;

      if (operationsInCurrentBatch >= batchSize) {
        batches.add(currentBatch);
        currentBatch = _firestore.batch();
        operationsInCurrentBatch = 0;
      }
    }

    if (operationsInCurrentBatch > 0) {
      batches.add(currentBatch);
    }

    for (final batch in batches) {
      await batch.commit();
    }
  }

  /// Helper function to follow users in batches
  Future<void> _followUsersBatch(
      String newUserId, List<String> targetUserIds) async {
    final batch = _firestore.batch();
    int operationCount = 0;

    for (final targetUserId in targetUserIds) {
      // Add to new user's following collection
      final followingRef = _firestore
          .collection('users')
          .doc(newUserId)
          .collection('following')
          .doc(targetUserId);

      batch.set(followingRef, {
        'followedAt': FieldValue.serverTimestamp(),
        'userId': targetUserId,
      });
      operationCount++;

      // Add to target user's followers collection
      final followerRef = _firestore
          .collection('users')
          .doc(targetUserId)
          .collection('followers')
          .doc(newUserId);

      batch.set(followerRef, {
        'followedAt': FieldValue.serverTimestamp(),
        'userId': newUserId,
      });
      operationCount++;

      // Update followers count for target user
      final targetUserRef = _firestore.collection('users').doc(targetUserId);
      batch.update(targetUserRef, {'followersCount': FieldValue.increment(1)});
      operationCount++;

      // Check if we're approaching batch limit
      if (operationCount >= 450) {
        // Conservative limit
        break;
      }
    }

    // Update following count for new user (once per batch)
    final newUserRef = _firestore.collection('users').doc(newUserId);
    batch.update(newUserRef,
        {'followingCount': FieldValue.increment(targetUserIds.length)});

    await batch.commit();
  }

  /// Alternative implementation: Follow all users with better error handling and progress callback
  Future<bool> followAllExistingUsersWithProgress(
    String newUserId, {
    Function(int current, int total)? onProgress,
  }) async {
    if (newUserId.isEmpty) {
      debugPrint('FollowService: Invalid user ID provided');
      return false;
    }

    try {
      debugPrint('FollowService: Starting to follow all users for: $newUserId');

      // Get all existing users (excluding the new user)
      final allUsersQuery = await _firestore
          .collection('users')
          .where(FieldPath.documentId, isNotEqualTo: newUserId)
          .get();

      if (allUsersQuery.docs.isEmpty) {
        debugPrint('FollowService: No existing users found to follow');
        return true;
      }

      final userIds = allUsersQuery.docs.map((doc) => doc.id).toList();
      final totalUsers = userIds.length;

      debugPrint('FollowService: Found $totalUsers users to follow');
      onProgress?.call(0, totalUsers);

      // Process users in smaller batches with progress updates
      const batchSize = 50;
      int processedCount = 0;

      for (int i = 0; i < userIds.length; i += batchSize) {
        final batchUserIds = userIds.skip(i).take(batchSize).toList();

        try {
          await _followUsersBatch(newUserId, batchUserIds);
          processedCount += batchUserIds.length;
          onProgress?.call(processedCount, totalUsers);

          // Small delay between batches to prevent overwhelming Firestore
          await Future.delayed(const Duration(milliseconds: 100));
        } catch (e) {
          debugPrint(
              'FollowService: Error in batch ${(i / batchSize).floor() + 1}: $e');
          // Continue with next batch instead of failing completely
        }
      }

      debugPrint(
          'FollowService: Successfully processed $processedCount out of $totalUsers users');
      return processedCount >
          0; // Return true if at least some users were followed
    } catch (e) {
      debugPrint('FollowService: Error following all users: $e');
      return false;
    }
  }

  /// Utility function to get total user count (useful for UI)
  Future<int> getTotalUserCount() async {
    try {
      final snapshot = await _firestore.collection('users').get();
      return snapshot.docs.length;
    } catch (e) {
      debugPrint('FollowService: Error getting user count: $e');
      return 0;
    }
  }
}
