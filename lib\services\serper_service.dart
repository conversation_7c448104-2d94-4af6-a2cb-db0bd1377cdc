import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:moneymouthy/constants/constants.dart';

class SerperService {
  final String _apiKey = '03ddc6608d3ce2ec6ec20ada5cce1e4e80177b2a';
  final String _baseUrl = 'https://google.serper.dev/images';

  Future<List<String>> searchImages(String query) async {
    if (_apiKey.isEmpty) {
      throw Exception('Serper API key not found in .env');
    }

    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'X-API-KEY': _apiKey,
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'q': query,
          'num': 50,
          'type': 'images',
          'location': 'United States'
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final images = data['images'] as List<dynamic>;
        return images
            .map((image) => kProxyUrl + (image['imageUrl']?.toString() ?? ''))
            .where((url) => url.isNotEmpty)
            .take(50)
            .toList();
      } else {
        throw Exception('Failed to fetch images: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error searching images: $e');
      throw Exception('Failed to fetch images: $e');
    }
  }

  Future<Uint8List?> downloadImage(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        return response.bodyBytes;
      }
      return null;
    } catch (e) {
      debugPrint('Error downloading image: $e');
      return null;
    }
  }
}
